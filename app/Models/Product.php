<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'store_variant_id', 'owner_id', 'sku', 'code', 'name', 'enabled', 'slug', 'release_date', 'package',
        'current_price', 'original_price', 'minimum_price', 'lowest_price_before_discount',
        'price_history', 'store_product_updated_at', 'store_variant_updated_at'
    ];

    protected $casts = [
        'enabled' => 'boolean',
        'price_history' => 'array',
        'release_date' => 'datetime',
        'store_product_updated_at' => 'datetime',
        'store_variant_updated_at' => 'datetime',
    ];

    /**
     * Get the organisation that owns this product.
     */
    public function organisation(): BelongsTo
    {
        return $this->belongsTo(Organisation::class, 'owner_id', 'code');
    }
}
