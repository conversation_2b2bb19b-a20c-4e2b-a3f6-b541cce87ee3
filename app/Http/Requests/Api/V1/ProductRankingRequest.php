<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

/**
 * Product Ranking Request
 *
 * Validates product ranking report parameters including limit for result count.
 * Inherits all filtering parameters from ReportFilterRequest.
 */
final class ProductRankingRequest extends ReportFilterRequest
{
    /**
     * Get the validation rules that apply to the request
     */
    public function rules(): array
    {
        $parentRules = parent::rules();

        // Remove product_id from parent rules as it's not applicable for product ranking
        unset($parentRules['product_id']);

        // Explicitly prohibit product_id parameter for product ranking endpoint
        $parentRules['product_id'] = ['prohibited'];

        // Add limit validation rule
        $parentRules['limit'] = ['sometimes', 'integer', 'min:1', 'max:100'];

        return $parentRules;
    }

    /**
     * Get custom messages for validator errors
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'limit.integer' => __('validation.reports.limit_integer'),
            'limit.min' => __('validation.reports.limit_min'),
            'limit.max' => __('validation.reports.limit_max'),
            'product_id.prohibited' => __('validation.reports.product_id_not_allowed_for_ranking'),
        ]);
    }

    /**
     * Get custom attribute names for validator errors
     */
    public function attributes(): array
    {
        return array_merge(parent::attributes(), [
            'limit' => __('validation.attributes.limit'),
        ]);
    }

    /**
     * Prepare the data for validation
     */
    protected function prepareForValidation(): void
    {
        parent::prepareForValidation();

        // Set default limit if not provided
        if (!$this->has('limit')) {
            $this->merge(['limit' => 10]);
        }
    }

    /**
     * Get the validated data with processed values
     */
    public function getProcessedData(): array
    {
        $data = parent::getProcessedData();

        // Ensure limit is included in processed data
        $data['limit'] = $this->validated()['limit'] ?? 10;

        return $data;
    }
}
