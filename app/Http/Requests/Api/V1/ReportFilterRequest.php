<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use App\Constants\ErrorCodes;
use App\Http\Requests\ApiRequest;
use App\Models\Organisation;
use App\Services\OrganisationService;
use Carbon\Carbon;

/**
 * Report Filter Request
 *
 * Validates report filtering parameters including date ranges, grouping options,
 * country filters, and organization filters.
 */
class ReportFilterRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request
     */
    public function authorize(): bool
    {
        return true; // Authorization is handled in the controller
    }

    /**
     * Get the validation rules that apply to the request
     */
    public function rules(): array
    {
        $user = $this->user();
        $isSystemUser = $user && $user->hasSystemAdminAccess();

        return [
            'start_date' => ['required', 'date', 'before_or_equal:end_date'],
            'end_date' => ['required', 'date', 'after_or_equal:start_date', 'before_or_equal:today'],
            'group_by' => ['sometimes', 'string', 'in:day,week,month,quarter,year'],
            'countries' => ['sometimes', 'array'],
            'countries.*' => ['string', 'size:2'], // ISO 2-letter country codes
            'states' => ['sometimes', 'array'],
            'states.*' => ['string', 'in:completed,cancelled,processing,pending'],
            'payment_states' => ['sometimes', 'array'],
            'payment_states.*' => ['string', 'in:completed,pending,failed,cancelled'],
            'organisation_id' => $isSystemUser ? ['sometimes', 'integer', 'exists:organisations,id'] : ['required', 'integer', 'exists:organisations,id'],
            'currency' => ['sometimes', 'string', 'size:3'], // ISO currency code
            'timezone' => ['sometimes', 'string', 'timezone'],
            'include_refunds' => ['sometimes', 'boolean'],
            'refund_status' => ['sometimes', 'string', 'in:success,pending,failed'],
            'product_id' => ['sometimes', 'integer', 'exists:products,store_variant_id'],
        ];
    }

    /**
     * Get custom messages for validator errors
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'start_date.required' => __('validation.reports.start_date_required'),
            'end_date.required' => __('validation.reports.end_date_required'),
            'start_date.before_or_equal' => __('validation.reports.invalid_date_range'),
            'end_date.before_or_equal' => __('validation.reports.end_date_future'),
            'countries.*.size' => __('validation.reports.invalid_country_code'),
            'group_by.in' => __('validation.reports.invalid_group_by'),
            'states.*.in' => __('validation.reports.invalid_state'),
            'payment_states.*.in' => __('validation.reports.invalid_payment_state'),
            'organisation_id.required' => __('validation.reports.organisation_id_required'),
            'organisation_id.integer' => __('validation.reports.organisation_id_must_be_integer'),
            'organisation_id.exists' => __('validation.reports.organisation_not_found'),
            'currency.size' => __('validation.reports.invalid_currency_code'),
            'timezone.timezone' => __('validation.reports.invalid_timezone'),
            'product_id.integer' => __('validation.reports.product_id_must_be_integer'),
            'product_id.exists' => __('validation.reports.product_not_found'),
        ]);
    }

    /**
     * Get custom attribute names for validator errors
     */
    public function attributes(): array
    {
        return array_merge(parent::attributes(), [
            'start_date' => __('validation.attributes.start_date'),
            'end_date' => __('validation.attributes.end_date'),
            'group_by' => __('validation.attributes.group_by'),
            'countries' => __('validation.attributes.countries'),
            'states' => __('validation.attributes.states'),
            'payment_states' => __('validation.attributes.payment_states'),
            'organisation_id' => __('validation.attributes.organisation_id'),
            'currency' => __('validation.attributes.currency'),
            'timezone' => __('validation.attributes.timezone'),
            'include_refunds' => __('validation.attributes.include_refunds'),
            'refund_status' => __('validation.attributes.refund_status'),
            'product_id' => __('validation.attributes.product_id'),
        ]);
    }

    /**
     * Configure the validator instance
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $this->validateOrganisationAccess($validator);
            $this->validateProductAccess($validator);
        });
    }

    /**
     * Check if user has access to the specified organisation
     */
    protected function hasOrganisationAccess(?object $user, ?int $organisationId): bool
    {
        // System admins can access any organisation
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        if (empty($organisationId) || !$user) {
            return false;
        }

        // Check if user belongs to the specified organisation
        return $user->belongsToOrganisation($organisationId);
    }

    /**
     * Validate organisation access permissions
     */
    protected function validateOrganisationAccess($validator): void
    {
        $organisationId = $this->input('organisation_id');

        if (!$this->hasOrganisationAccess($this->user(), (int) $organisationId)) {
            $validator->errors()->add(
                'organisation_id',
                __('errors.report_organisation_access_denied')
            );
        }
    }

    /**
     * Validate product access permissions
     */
    protected function validateProductAccess($validator): void
    {
        $productId = $this->input('product_id');

        // Skip validation if no product_id provided
        if (empty($productId)) {
            return;
        }

        $organisationId = $this->input('organisation_id');



        // Only validate product access if user has access to the organisation
        if (!$this->hasOrganisationAccess($this->user(), (int) $organisationId)) {
            // Don't add product error if organisation access already failed
            // The organisation validation will handle this
            return;
        }

        // Get user's accessible product IDs for the organisation
        $allowedProductIds = $this->getUserAccessibleProductIds((int) $organisationId);

        // Check if the requested product_id is in the user's allowed list
        if (!in_array((int) $productId, $allowedProductIds, true)) {
            $validator->errors()->add(
                'product_id',
                __('errors.report_product_access_denied')
            );
        }
    }

    /**
     * Get product IDs that the user can access for reports in the specified organisation
     */
    protected function getUserAccessibleProductIds(int $organisationId): array
    {
        $user = $this->user();

        // If user doesn't have organisation access, return empty array
        if (!$this->hasOrganisationAccess($user, $organisationId)) {
            return [];
        }

        // Both system admins and organisation members can access all organisation products
        // This can be extended in the future to support granular product permissions
        $organisationService = app(OrganisationService::class);
        return $organisationService->getOrganisationProductIds($organisationId);
    }

    /**
     * Prepare the data for validation
     */
    protected function prepareForValidation(): void
    {
        // Set default timezone if not provided
        if (!$this->has('timezone')) {
            $this->merge(['timezone' => config('app.timezone')]);
        }

        // Set default group_by if not provided
        if (!$this->has('group_by')) {
            $this->merge(['group_by' => 'day']);
        }

        // Set default currency if not provided
        if (!$this->has('currency')) {
            $this->merge(['currency' => 'USD']);
        }

        // Convert include_refunds to boolean if provided as string
        if ($this->has('include_refunds') && is_string($this->include_refunds)) {
            $this->merge(['include_refunds' => filter_var($this->include_refunds, FILTER_VALIDATE_BOOLEAN)]);
        }
    }

    /**
     * Get the validated data with processed values
     */
    public function getProcessedData(): array
    {
        $data = $this->validated();

        // Ensure dates are in proper format
        if (isset($data['start_date'])) {
            $data['start_date'] = Carbon::parse($data['start_date'])->startOfDay()->toDateTimeString();
        }
        if (isset($data['end_date'])) {
            $data['end_date'] = Carbon::parse($data['end_date'])->endOfDay()->toDateTimeString();
        }

        // Handle product filtering
        $organisationService = app(OrganisationService::class);
        $organisationId = $this->input('organisation_id');

        if (isset($data['product_id'])) {
            // If specific product_id is provided, use only that product
            $data['product_ids'] = [(int) $data['product_id']];
            // Remove the single product_id from the data array
            unset($data['product_id']);
        } else {
            // Use all organisation products for filtering
            $data['product_ids'] = $organisationService->getOrganisationProductIds($organisationId ? (int) $organisationId : null);
        }

        return $data;
    }
}
