<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\Api\V1\ReportFilterRequest;
use App\Http\Requests\Api\V1\ReportExportRequest;
use App\Http\Requests\Api\V1\ProductRankingRequest;
use App\Http\Resources\Api\V1\SalesReportResource;
use App\Http\Resources\Api\V1\VolumeReportResource;
use App\Http\Resources\Api\V1\RefundReportResource;
use App\Http\Resources\Api\V1\OrderStatusReportResource;
use App\Http\Resources\Api\V1\ProductRankingReportResource;
use App\Services\ReportService;
use Illuminate\Http\JsonResponse;

/**
 * Report Controller
 *
 * Handles data reporting endpoints including sales, volume, refunds, and order status reports.
 * Implements role-based access control based on the existing four-tier permission system.
 */
final class ReportController extends ApiController
{
    public function __construct(
        private readonly ReportService $reportService
    ) {
        // Apply authorization policy for all report methods
        $this->middleware('auth:sanctum');
    }

    /**
     * Get sales report data
     *
     * Returns sales amount statistics grouped by time period and region
     */
    public function sales(ReportFilterRequest $request): JsonResponse
    {
        $organisationId = $request->input('organisation_id');

        // Use Laravel's standard authorization pattern
        $this->authorize('viewAny', ['report', $organisationId]);

        $processedData = $request->getProcessedData();
        $data = $this->reportService->getSalesReport($processedData);

        return $this->successResponse(
            new SalesReportResource($data),
            'api.reports.sales_retrieved'
        );
    }

    /**
     * Get volume report data
     *
     * Returns sales volume statistics grouped by time period and region
     */
    public function volume(ReportFilterRequest $request): JsonResponse
    {
        $organisationId = $request->input('organisation_id');

        // Use Laravel's standard authorization pattern
        $this->authorize('viewAny', ['report', $organisationId]);

        $processedData = $request->getProcessedData();
        $data = $this->reportService->getVolumeReport($processedData);

        return $this->successResponse(
            new VolumeReportResource($data),
            'api.reports.volume_retrieved'
        );
    }

    /**
     * Get refund analysis report
     *
     * Returns refund statistics and analysis data
     */
    public function refunds(ReportFilterRequest $request): JsonResponse
    {
        $organisationId = $request->input('organisation_id');

        // Use Laravel's standard authorization pattern
        $this->authorize('viewAny', ['report', $organisationId]);

        $processedData = $request->getProcessedData();
        $data = $this->reportService->getRefundReport($processedData);

        return $this->successResponse(
            new RefundReportResource($data),
            'api.reports.refunds_retrieved'
        );
    }

    /**
     * Get order status report
     *
     * Returns order status distribution and statistics
     */
    public function orderStatus(ReportFilterRequest $request): JsonResponse
    {
        $organisationId = $request->input('organisation_id');

        // Use Laravel's standard authorization pattern
        $this->authorize('viewAny', ['report', $organisationId]);

        $processedData = $request->getProcessedData();
        $data = $this->reportService->getOrderStatusReport($processedData);

        return $this->successResponse(
            new OrderStatusReportResource($data),
            'api.reports.order_status_retrieved'
        );
    }

    /**
     * Get product ranking report
     *
     * Returns product sales ranking by store_variant_id with product details
     */
    public function productRanking(ProductRankingRequest $request): JsonResponse
    {
        $organisationId = $request->input('organisation_id');

        // Use Laravel's standard authorization pattern
        $this->authorize('viewAny', ['report', $organisationId]);

        $processedData = $request->getProcessedData();
        $data = $this->reportService->getProductRankingReport($processedData);

        return $this->successResponse(
            new ProductRankingReportResource($data),
            'api.reports.product_ranking_retrieved'
        );
    }

    /**
     * Export report data
     *
     * Generates and returns downloadable report files
     */
    public function export(ReportExportRequest $request): JsonResponse
    {
        $organisationId = $request->input('organisation_id');

        // Use Laravel's standard authorization pattern
        $this->authorize('exportForOrganisation', ['report', (int) $organisationId]);

        // Get processed data with product IDs included
        $processedData = $request->getProcessedData();

        // Merge with export-specific configuration
        $exportConfig = $request->getExportConfig();
        $requestData = array_merge($processedData, $exportConfig);

        $result = $this->reportService->exportReport($requestData);

        return $this->successResponse(
            $result,
            'api.reports.export_started'
        );
    }


}
