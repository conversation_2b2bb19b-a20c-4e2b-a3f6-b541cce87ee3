<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

/**
 * Sales Report Service
 * 
 * Handles generation of sales and volume reports with various grouping and aggregation options.
 */
final class SalesReportService
{
    /**
     * Generate sales report
     */
    public function generateReport(Builder $query, array $filters): array
    {
        $groupBy = $filters['group_by'] ?? 'day';

        // Get summary data
        $summary = $this->getSummaryData(clone $query);

        // Get grouped data
        $chartData = $this->getGroupedData(clone $query, $groupBy, $filters);

        // Get region data
        $regionData = $this->getRegionData(clone $query);

        return [
            'summary' => $summary,
            'chart_data' => $chartData->toArray(),
            'region_data' => $regionData->toArray(),
            'meta' => [
                'period' => $filters['start_date'] . ' to ' . $filters['end_date'],
                'group_by' => $groupBy,
                'total_records' => $chartData->count(),
                'currency' => $filters['currency'] ?? 'USD',
                'timezone' => $filters['timezone'] ?? config('app.timezone'),
            ]
        ];
    }

    /**
     * Generate volume report
     */
    public function generateVolumeReport(Builder $query, array $filters): array
    {
        $groupBy = $filters['group_by'] ?? 'day';

        // Get volume summary data
        $summary = $this->getVolumeSummaryData(clone $query);

        // Get grouped volume data
        $chartData = $this->getGroupedVolumeData(clone $query, $groupBy, $filters);

        // Get region volume data
        $regionData = $this->getRegionVolumeData(clone $query);

        return [
            'summary' => $summary,
            'chart_data' => $chartData->toArray(),
            'region_data' => $regionData->toArray(),
            'meta' => [
                'period' => $filters['start_date'] . ' to ' . $filters['end_date'],
                'group_by' => $groupBy,
                'total_records' => $chartData->count(),
                'timezone' => $filters['timezone'] ?? config('app.timezone'),
            ]
        ];
    }

    /**
     * Get summary data for sales report
     */
    private function getSummaryData(Builder $query): array
    {
        $summary = $query->selectRaw('
            COUNT(*) as total_orders,
            SUM(total_amount) as total_sales,
            AVG(total_amount) as average_order_value,
            SUM(CASE WHEN refund_status = "success" THEN 1 ELSE 0 END) as refunded_orders,
            SUM(CASE WHEN refund_status = "success" THEN refund_total ELSE 0 END) as total_refunds
        ')->first();

        return [
            'total_orders' => (int) ($summary->total_orders ?? 0),
            'total_sales' => (int) ($summary->total_sales ?? 0),
            'total_sales_formatted' => number_format(($summary->total_sales ?? 0) / 100, 2),
            'average_order_value' => (int) ($summary->average_order_value ?? 0),
            'average_order_value_formatted' => number_format(($summary->average_order_value ?? 0) / 100, 2),
            'refunded_orders' => (int) ($summary->refunded_orders ?? 0),
            'total_refunds' => (int) ($summary->total_refunds ?? 0),
            'total_refunds_formatted' => number_format(($summary->total_refunds ?? 0) / 100, 2),
            'refund_rate' => ($summary->total_orders ?? 0) > 0
                ? round(((float) ($summary->refunded_orders ?? 0) / (float) ($summary->total_orders ?? 0)) * 100, 2)
                : 0,
            'net_sales' => (int) (($summary->total_sales ?? 0) - ($summary->total_refunds ?? 0)),
            'net_sales_formatted' => number_format((($summary->total_sales ?? 0) - ($summary->total_refunds ?? 0)) / 100, 2),
        ];
    }

    /**
     * Get volume summary data
     */
    private function getVolumeSummaryData(Builder $query): array
    {
        $summary = $query->join('order_items', 'orders.id', '=', 'order_items.order_id')
            ->selectRaw('
                COUNT(DISTINCT orders.id) as total_orders,
                SUM(order_items.quantity) as total_quantity,
                AVG(order_items.quantity) as average_quantity_per_order,
                SUM(order_items.quantity_refunded) as total_refunded_quantity
            ')->first();

        return [
            'total_orders' => (int) ($summary->total_orders ?? 0),
            'total_quantity' => (int) ($summary->total_quantity ?? 0),
            'average_quantity_per_order' => round((float) ($summary->average_quantity_per_order ?? 0), 2),
            'total_refunded_quantity' => (int) ($summary->total_refunded_quantity ?? 0),
            'net_quantity' => (int) (($summary->total_quantity ?? 0) - ($summary->total_refunded_quantity ?? 0)),
            'refund_quantity_rate' => ($summary->total_quantity ?? 0) > 0
                ? round(((float) ($summary->total_refunded_quantity ?? 0) / (float) ($summary->total_quantity ?? 0)) * 100, 2)
                : 0,
        ];
    }

    /**
     * Get grouped data by time period
     */
    private function getGroupedData(Builder $query, string $groupBy, array $filters): \Illuminate\Support\Collection
    {
        $dateFormat = $this->getDateFormat($groupBy);
        $timezone = $filters['timezone'] ?? config('app.timezone');

        return $query->selectRaw("
            DATE_FORMAT(CONVERT_TZ(completed_at, '+00:00', ?), ?) as period,
            COUNT(*) as order_count,
            SUM(total_amount) as total_sales,
            AVG(total_amount) as average_order_value,
            SUM(CASE WHEN refund_status = 'success' THEN 1 ELSE 0 END) as refunded_orders,
            SUM(CASE WHEN refund_status = 'success' THEN refund_total ELSE 0 END) as total_refunds
        ", [$timezone, $dateFormat])
        ->groupBy('period')
        ->orderBy('period')
        ->get()
        ->map(function ($item) {
            $netSales = $item->total_sales - $item->total_refunds;
            return [
                'period' => $item->period,
                'order_count' => (int) $item->order_count,
                'total_sales' => (int) $item->total_sales,
                'total_sales_formatted' => number_format($item->total_sales / 100, 2),
                'average_order_value' => (int) $item->average_order_value,
                'average_order_value_formatted' => number_format($item->average_order_value / 100, 2),
                'refunded_orders' => (int) $item->refunded_orders,
                'total_refunds' => (int) $item->total_refunds,
                'total_refunds_formatted' => number_format($item->total_refunds / 100, 2),
                'net_sales' => (int) $netSales,
                'net_sales_formatted' => number_format($netSales / 100, 2),
            ];
        });
    }

    /**
     * Get grouped volume data by time period
     */
    private function getGroupedVolumeData(Builder $query, string $groupBy, array $filters): \Illuminate\Support\Collection
    {
        $dateFormat = $this->getDateFormat($groupBy);
        $timezone = $filters['timezone'] ?? config('app.timezone');

        return $query->join('order_items', 'orders.id', '=', 'order_items.order_id')
            ->selectRaw("
                DATE_FORMAT(CONVERT_TZ(orders.completed_at, '+00:00', ?), ?) as period,
                COUNT(DISTINCT orders.id) as order_count,
                SUM(order_items.quantity) as total_quantity,
                AVG(order_items.quantity) as average_quantity,
                SUM(order_items.quantity_refunded) as refunded_quantity
            ", [$timezone, $dateFormat])
            ->groupBy('period')
            ->orderBy('period')
            ->get()
            ->map(function ($item) {
                $netQuantity = $item->total_quantity - $item->refunded_quantity;
                return [
                    'period' => $item->period,
                    'order_count' => (int) $item->order_count,
                    'total_quantity' => (int) $item->total_quantity,
                    'average_quantity' => round((float) $item->average_quantity, 2),
                    'refunded_quantity' => (int) $item->refunded_quantity,
                    'net_quantity' => (int) $netQuantity,
                ];
            });
    }

    /**
     * Get region distribution data
     */
    private function getRegionData(Builder $query): \Illuminate\Support\Collection
    {
        return $query->selectRaw('
            shipping_country,
            COUNT(*) as order_count,
            SUM(total_amount) as total_sales,
            AVG(total_amount) as average_order_value,
            SUM(CASE WHEN refund_status = "success" THEN refund_total ELSE 0 END) as total_refunds
        ')
        ->whereNotNull('shipping_country')
        ->groupBy('shipping_country')
        ->orderByDesc('total_sales')
        ->get()
        ->map(function ($item) {
            $netSales = $item->total_sales - $item->total_refunds;
            return [
                'country' => $item->shipping_country,
                'order_count' => (int) $item->order_count,
                'total_sales' => (int) $item->total_sales,
                'total_sales_formatted' => number_format($item->total_sales / 100, 2),
                'average_order_value' => (int) $item->average_order_value,
                'average_order_value_formatted' => number_format($item->average_order_value / 100, 2),
                'total_refunds' => (int) $item->total_refunds,
                'total_refunds_formatted' => number_format($item->total_refunds / 100, 2),
                'net_sales' => (int) $netSales,
                'net_sales_formatted' => number_format($netSales / 100, 2),
            ];
        });
    }

    /**
     * Get region volume distribution data
     */
    private function getRegionVolumeData(Builder $query): \Illuminate\Support\Collection
    {
        return $query->join('order_items', 'orders.id', '=', 'order_items.order_id')
            ->selectRaw('
                orders.shipping_country,
                COUNT(DISTINCT orders.id) as order_count,
                SUM(order_items.quantity) as total_quantity,
                AVG(order_items.quantity) as average_quantity,
                SUM(order_items.quantity_refunded) as refunded_quantity
            ')
            ->whereNotNull('orders.shipping_country')
            ->groupBy('orders.shipping_country')
            ->orderByDesc('total_quantity')
            ->get()
            ->map(function ($item) {
                $netQuantity = $item->total_quantity - $item->refunded_quantity;
                return [
                    'country' => $item->shipping_country,
                    'order_count' => (int) $item->order_count,
                    'total_quantity' => (int) $item->total_quantity,
                    'average_quantity' => round((float) $item->average_quantity, 2),
                    'refunded_quantity' => (int) $item->refunded_quantity,
                    'net_quantity' => (int) $netQuantity,
                ];
            });
    }

    /**
     * Get date format for MySQL based on grouping type
     */
    private function getDateFormat(string $groupBy): string
    {
        return match ($groupBy) {
            'day' => '%Y-%m-%d',
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            'quarter' => '%Y-Q%q',
            'year' => '%Y',
            default => '%Y-%m-%d',
        };
    }
}
