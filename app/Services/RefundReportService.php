<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

/**
 * Refund Report Service
 * 
 * Handles generation of refund analysis reports including refund rates,
 * trends, and detailed refund statistics.
 */
final class RefundReportService
{
    /**
     * Generate refund report
     */
    public function generateReport(Builder $query, array $filters): array
    {
        $groupBy = $filters['group_by'] ?? 'day';

        // Get refund summary data
        $summary = $this->getRefundSummaryData(clone $query);

        // Get grouped refund data
        $chartData = $this->getGroupedRefundData(clone $query, $groupBy, $filters);

        // Get refund reasons analysis (placeholder for now)
        $reasonsData = $this->getRefundReasonsData(clone $query);

        // Get region refund data
        $regionData = $this->getRegionRefundData(clone $query);

        return [
            'summary' => $summary,
            'chart_data' => $chartData->toArray(),
            'reasons_data' => $reasonsData->toArray(),
            'region_data' => $regionData->toArray(),
            'meta' => [
                'period' => $filters['start_date'] . ' to ' . $filters['end_date'],
                'group_by' => $groupBy,
                'total_records' => $chartData->count(),
                'currency' => $filters['currency'] ?? 'USD',
                'timezone' => $filters['timezone'] ?? config('app.timezone'),
            ]
        ];
    }

    /**
     * Get refund summary data
     */
    private function getRefundSummaryData(Builder $query): array
    {
        $summary = $query->selectRaw('
            COUNT(*) as total_orders,
            SUM(total_amount) as total_sales,
            SUM(CASE WHEN refund_status = "success" THEN 1 ELSE 0 END) as refunded_orders,
            SUM(CASE WHEN refund_status = "success" THEN refund_total ELSE 0 END) as total_refunds,
            SUM(CASE WHEN refund_status = "pending" THEN 1 ELSE 0 END) as pending_refunds,
            SUM(CASE WHEN refund_status = "failed" THEN 1 ELSE 0 END) as failed_refunds
        ')->first();

        $refundRate = ($summary->total_orders ?? 0) > 0
            ? round(((float) ($summary->refunded_orders ?? 0) / (float) ($summary->total_orders ?? 0)) * 100, 2)
            : 0;

        $refundAmountRate = ($summary->total_sales ?? 0) > 0
            ? round(((float) ($summary->total_refunds ?? 0) / (float) ($summary->total_sales ?? 0)) * 100, 2)
            : 0;

        return [
            'total_orders' => (int) ($summary->total_orders ?? 0),
            'total_sales' => (int) ($summary->total_sales ?? 0),
            'total_sales_formatted' => number_format(($summary->total_sales ?? 0) / 100, 2),
            'refunded_orders' => (int) ($summary->refunded_orders ?? 0),
            'pending_refunds' => (int) ($summary->pending_refunds ?? 0),
            'failed_refunds' => (int) ($summary->failed_refunds ?? 0),
            'total_refunds' => (int) ($summary->total_refunds ?? 0),
            'total_refunds_formatted' => number_format(($summary->total_refunds ?? 0) / 100, 2),
            'refund_rate' => $refundRate,
            'refund_amount_rate' => $refundAmountRate,
            'average_refund_amount' => ($summary->refunded_orders ?? 0) > 0
                ? (int) (($summary->total_refunds ?? 0) / ($summary->refunded_orders ?? 0))
                : 0,
            'average_refund_amount_formatted' => ($summary->refunded_orders ?? 0) > 0
                ? number_format((($summary->total_refunds ?? 0) / ($summary->refunded_orders ?? 0)) / 100, 2)
                : '0.00',
        ];
    }

    /**
     * Get grouped refund data by time period
     */
    private function getGroupedRefundData(Builder $query, string $groupBy, array $filters): \Illuminate\Support\Collection
    {
        $dateFormat = $this->getDateFormat($groupBy);
        $timezone = $filters['timezone'] ?? config('app.timezone');

        return $query->selectRaw("
            DATE_FORMAT(CONVERT_TZ(completed_at, '+00:00', ?), ?) as period,
            COUNT(*) as total_orders,
            SUM(total_amount) as total_sales,
            SUM(CASE WHEN refund_status = 'success' THEN 1 ELSE 0 END) as refunded_orders,
            SUM(CASE WHEN refund_status = 'success' THEN refund_total ELSE 0 END) as total_refunds,
            SUM(CASE WHEN refund_status = 'pending' THEN 1 ELSE 0 END) as pending_refunds,
            SUM(CASE WHEN refund_status = 'failed' THEN 1 ELSE 0 END) as failed_refunds
        ", [$timezone, $dateFormat])
        ->groupBy('period')
        ->orderBy('period')
        ->get()
        ->map(function ($item) {
            $refundRate = ($item->total_orders ?? 0) > 0
                ? round(((float) ($item->refunded_orders ?? 0) / (float) ($item->total_orders ?? 0)) * 100, 2)
                : 0;

            $refundAmountRate = ($item->total_sales ?? 0) > 0
                ? round(((float) ($item->total_refunds ?? 0) / (float) ($item->total_sales ?? 0)) * 100, 2)
                : 0;

            return [
                'period' => $item->period,
                'total_orders' => (int) ($item->total_orders ?? 0),
                'total_sales' => (int) ($item->total_sales ?? 0),
                'total_sales_formatted' => number_format(($item->total_sales ?? 0) / 100, 2),
                'refunded_orders' => (int) ($item->refunded_orders ?? 0),
                'pending_refunds' => (int) ($item->pending_refunds ?? 0),
                'failed_refunds' => (int) ($item->failed_refunds ?? 0),
                'total_refunds' => (int) ($item->total_refunds ?? 0),
                'total_refunds_formatted' => number_format(($item->total_refunds ?? 0) / 100, 2),
                'refund_rate' => $refundRate,
                'refund_amount_rate' => $refundAmountRate,
            ];
        });
    }

    /**
     * Get refund reasons analysis
     * Note: This is a placeholder as the current schema doesn't have detailed refund reasons
     */
    private function getRefundReasonsData(Builder $query): \Illuminate\Support\Collection
    {
        return $query->selectRaw('
            COALESCE(refund_comment, "No reason provided") as reason,
            COUNT(*) as refund_count,
            SUM(refund_total) as total_refund_amount
        ')
        ->where('refund_status', 'success')
        ->groupBy('refund_comment')
        ->orderByDesc('refund_count')
        ->get()
        ->map(function ($item) {
            return [
                'reason' => $item->reason ?: 'No reason provided',
                'refund_count' => (int) ($item->refund_count ?? 0),
                'total_refund_amount' => (int) ($item->total_refund_amount ?? 0),
                'total_refund_amount_formatted' => number_format(($item->total_refund_amount ?? 0) / 100, 2),
            ];
        });
    }

    /**
     * Get region refund distribution data
     */
    private function getRegionRefundData(Builder $query): \Illuminate\Support\Collection
    {
        return $query->selectRaw('
            shipping_country,
            COUNT(*) as total_orders,
            SUM(total_amount) as total_sales,
            SUM(CASE WHEN refund_status = "success" THEN 1 ELSE 0 END) as refunded_orders,
            SUM(CASE WHEN refund_status = "success" THEN refund_total ELSE 0 END) as total_refunds,
            SUM(CASE WHEN refund_status = "pending" THEN 1 ELSE 0 END) as pending_refunds,
            SUM(CASE WHEN refund_status = "failed" THEN 1 ELSE 0 END) as failed_refunds
        ')
        ->whereNotNull('shipping_country')
        ->groupBy('shipping_country')
        ->orderByDesc('refunded_orders')
        ->get()
        ->map(function ($item) {
            $refundRate = ($item->total_orders ?? 0) > 0
                ? round(((float) ($item->refunded_orders ?? 0) / (float) ($item->total_orders ?? 0)) * 100, 2)
                : 0;

            $refundAmountRate = ($item->total_sales ?? 0) > 0
                ? round(((float) ($item->total_refunds ?? 0) / (float) ($item->total_sales ?? 0)) * 100, 2)
                : 0;

            return [
                'country' => $item->shipping_country,
                'total_orders' => (int) ($item->total_orders ?? 0),
                'total_sales' => (int) ($item->total_sales ?? 0),
                'total_sales_formatted' => number_format(($item->total_sales ?? 0) / 100, 2),
                'refunded_orders' => (int) ($item->refunded_orders ?? 0),
                'pending_refunds' => (int) ($item->pending_refunds ?? 0),
                'failed_refunds' => (int) ($item->failed_refunds ?? 0),
                'total_refunds' => (int) ($item->total_refunds ?? 0),
                'total_refunds_formatted' => number_format(($item->total_refunds ?? 0) / 100, 2),
                'refund_rate' => $refundRate,
                'refund_amount_rate' => $refundAmountRate,
            ];
        });
    }

    /**
     * Get date format for MySQL based on grouping type
     */
    private function getDateFormat(string $groupBy): string
    {
        return match ($groupBy) {
            'day' => '%Y-%m-%d',
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            'quarter' => '%Y-Q%q',
            'year' => '%Y',
            default => '%Y-%m-%d',
        };
    }
}
