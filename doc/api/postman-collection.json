{"info": {"name": "JAST Partner API", "description": "Complete API collection for JAST Partner application", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health & Status", "item": [{"name": "Global Health Check", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/health", "host": ["{{baseURL}}"], "path": ["api", "health"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has status field\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "});"]}}]}, {"name": "API v1 Health Check", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/health", "host": ["{{baseURL}}"], "path": ["api", "v1", "health"]}}}, {"name": "API v1 Status", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/status", "host": ["{{baseURL}}"], "path": ["api", "v1", "status"]}}}]}, {"name": "Authentication & Registration", "item": [{"name": "Send Verification Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{testEmail}}\"\n}"}, "url": {"raw": "{{baseURL}}/api/v1/users/send-verification-code", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "send-verification-code"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 or 422\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 422]);", "});", "", "if (pm.response.code === 200) {", "    pm.test(\"Verification code sent successfully\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.success).to.eql(true);", "        pm.expect(jsonData.data.expires_in_seconds).to.exist;", "    });", "} else if (pm.response.code === 422) {", "    pm.test(\"Rate limit or validation error\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.success).to.eql(false);", "        pm.expect(jsonData.message).to.exist;", "    });", "}"]}}]}, {"name": "User Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test User\",\n  \"email\": \"{{testEmail}}\",\n  \"password\": \"{{testPassword}}\",\n  \"password_confirmation\": \"{{testPassword}}\",\n  \"verification_code\": \"123456\",\n  \"organisation_ids\": []\n}"}, "url": {"raw": "{{baseURL}}/api/v1/users/register", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "register"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201 or 422\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([201, 422]);", "});", "", "if (pm.response.code === 201) {", "    pm.test(\"User registration successful\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.success).to.eql(true);", "        pm.expect(jsonData.data.id).to.exist;", "        pm.expect(jsonData.data.email_verified_at).to.exist;", "        pm.expect(jsonData.data.organisations).to.be.an('array');", "    });", "} else if (pm.response.code === 422) {", "    pm.test(\"Registration validation error\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.success).to.eql(false);", "        pm.expect(jsonData.errors).to.exist;", "    });", "}"]}}]}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{testEmail}}\",\n  \"password\": \"{{testPassword}}\"\n}"}, "url": {"raw": "{{baseURL}}/api/v1/auth/login", "host": ["{{baseURL}}"], "path": ["api", "v1", "auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Login successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.token).to.exist;", "    ", "    // Store token for other requests", "    pm.environment.set(\"authToken\", jsonData.data.token);", "});"]}}]}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseURL}}/api/v1/auth/logout", "host": ["{{baseURL}}"], "path": ["api", "v1", "auth", "logout"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Logout successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    ", "    // Clear token after logout", "    pm.environment.set(\"authToken\", \"\");", "});"]}}]}, {"name": "Revoke All Tokens", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseURL}}/api/v1/auth/revoke-all-tokens", "host": ["{{baseURL}}"], "path": ["api", "v1", "auth", "revoke-all-tokens"]}}}]}, {"name": "User Management", "description": "User management endpoints with role-based access control. System admins (Root/Admin) can manage all users, while organization owners can only manage users within their organizations.", "item": [{"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/user", "host": ["{{baseURL}}"], "path": ["api", "v1", "user"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get current user successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data).to.exist;", "    pm.expect(jsonData.data.organisations).to.be.an('array');", "    pm.expect(jsonData.data.roles).to.exist;", "    pm.expect(jsonData.data.roles.system_roles).to.be.an('array');", "    pm.expect(jsonData.data.roles.organisation_roles).to.be.an('array');", "});"]}}]}, {"name": "Get Users List", "description": "Get paginated list of users. System admins see all users, organization users see only users from their organizations.", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/users", "host": ["{{baseURL}}"], "path": ["api", "v1", "users"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get users list successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.data).to.be.an('array');", "});"]}}]}, {"name": "Get Users by Organisation", "description": "Filter users by organization ID. Organization users can only filter by their own organizations.", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/users?organisation_id={{organisationId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "users"], "query": [{"key": "organisation_id", "value": "{{organisationId}}"}]}}}, {"name": "Create User", "description": "Create a new user. System admins can create users in any organization, organization owners can only create users in their own organizations.", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test User\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"organisation_ids\": [{{organisationId}}]\n}"}, "url": {"raw": "{{baseURL}}/api/v1/users", "host": ["{{baseURL}}"], "path": ["api", "v1", "users"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Create user successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.id).to.exist;", "    pm.expect(jsonData.data.organisations).to.be.an('array');", "    ", "    // Store user ID for other requests", "    pm.environment.set(\"userId\", jsonData.data.id);", "});"]}}]}, {"name": "Get User Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/users/{{userId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "{{userId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get user details successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.id).to.exist;", "    pm.expect(jsonData.data.organisations).to.be.an('array');", "});"]}}]}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated User Name\",\n  \"organisation_ids\": [{{organisationId}}]\n}"}, "url": {"raw": "{{baseURL}}/api/v1/users/{{userId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "{{userId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Update user successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.name).to.eql('Updated User Name');", "});"]}}]}, {"name": "Suspend User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseURL}}/api/v1/users/{{userId}}/suspend", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "{{userId}}", "suspend"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Suspend user successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "});"]}}]}, {"name": "Activate User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseURL}}/api/v1/users/{{userId}}/activate", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "{{userId}}", "activate"]}}}, {"name": "Add User to Organisation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseURL}}/api/v1/users/{{userId}}/organisations/{{organisationId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "{{userId}}", "organisations", "{{organisationId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Add user to organisation successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "});"]}}]}, {"name": "Remove User from Organisation", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/users/{{userId}}/organisations/{{organisationId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "{{userId}}", "organisations", "{{organisationId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Remove user from organisation successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "});"]}}]}, {"name": "Sync User Organisations", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"organisation_ids\": [{{organisationId}}]\n}"}, "url": {"raw": "{{baseURL}}/api/v1/users/{{userId}}/organisations", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "{{userId}}", "organisations"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Sync user organisations successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "});"]}}]}]}, {"name": "Organisation Management", "item": [{"name": "Get Organisations List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/organisations", "host": ["{{baseURL}}"], "path": ["api", "v1", "organisations"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get organisations list successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data).to.exist;", "    pm.expect(jsonData.data.data).to.be.an('array');", "});"]}}]}, {"name": "Get Organisations by Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/organisations?status=active", "host": ["{{baseURL}}"], "path": ["api", "v1", "organisations"], "query": [{"key": "status", "value": "active"}]}}}, {"name": "Create Organisation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Organisation\",\n  \"code\": \"TEST001\",\n  \"details\": {\n    \"industry\": \"Technology\",\n    \"size\": \"Medium\"\n  },\n  \"remarks\": \"Test organisation for API testing\"\n}"}, "url": {"raw": "{{baseURL}}/api/v1/organisations", "host": ["{{baseURL}}"], "path": ["api", "v1", "organisations"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Create organisation successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.id).to.exist;", "    pm.expect(jsonData.data.status).to.eql('pending');", "    ", "    // Store organisation ID for other requests", "    pm.environment.set(\"organisationId\", jsonData.data.id);", "});"]}}]}, {"name": "Get Organisation Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/organisations/{{organisationId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "organisations", "{{organisationId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get organisation details successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.id).to.exist;", "});"]}}]}, {"name": "Update Organisation", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Organisation Name\",\n  \"details\": {\n    \"industry\": \"Technology\",\n    \"size\": \"Large\"\n  },\n  \"remarks\": \"Updated remarks\"\n}"}, "url": {"raw": "{{baseURL}}/api/v1/organisations/{{organisationId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "organisations", "{{organisationId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Update organisation successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.name).to.eql('Updated Organisation Name');", "});"]}}]}, {"name": "Suspend Organisation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseURL}}/api/v1/organisations/{{organisationId}}/suspend", "host": ["{{baseURL}}"], "path": ["api", "v1", "organisations", "{{organisationId}}", "suspend"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Suspend organisation successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.status).to.eql('suspended');", "    pm.expect(jsonData.data.is_suspended).to.eql(true);", "});"]}}]}]}, {"name": "Role Management", "item": [{"name": "Get Roles List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/roles", "host": ["{{baseURL}}"], "path": ["api", "v1", "roles"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get roles list successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data).to.be.an('array');", "});"]}}]}, {"name": "Create Role", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"custom-role\",\n  \"guard_name\": \"api\"\n}"}, "url": {"raw": "{{baseURL}}/api/v1/roles", "host": ["{{baseURL}}"], "path": ["api", "v1", "roles"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Create role successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.id).to.exist;", "    pm.expect(jsonData.data.name).to.eql('custom-role');", "    ", "    // Store role ID for other requests", "    pm.environment.set(\"roleId\", jsonData.data.id);", "});"]}}]}, {"name": "Get Role Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/roles/{{roleId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "roles", "{{roleId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get role details successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.id).to.exist;", "});"]}}]}, {"name": "Update Role", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"updated-custom-role\"\n}"}, "url": {"raw": "{{baseURL}}/api/v1/roles/{{roleId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "roles", "{{roleId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Update role successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.name).to.eql('updated-custom-role');", "});"]}}]}, {"name": "Delete Role", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/roles/{{roleId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "roles", "{{roleId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Delete role successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "});"]}}]}]}, {"name": "User Role Management", "description": "User role assignment and management endpoints with hierarchical role system. Root/Admin can manage all roles, while organization owners can only assign member roles within their organizations.", "item": [{"name": "Get Assignable Roles", "description": "Get list of roles that current user can assign to other users based on their permission level.", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/users/assignable-roles", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "assignable-roles"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get assignable roles successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data).to.be.an('array');", "});"]}}]}, {"name": "Assign Role to User", "description": "Assign a role to a user. Users can only assign roles lower than their own level.", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"role_id\": {{roleId}}\n}"}, "url": {"raw": "{{baseURL}}/api/v1/users/{{userId}}/roles", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "{{userId}}", "roles"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Assign role successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "});"]}}]}, {"name": "Remove Role from User", "description": "Remove a role from a user. Users can only remove roles lower than their own level.", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/users/{{userId}}/roles/{{roleId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "{{userId}}", "roles", "{{roleId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Remove role successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "});"]}}]}, {"name": "Get User Roles", "description": "Get detailed role information for a specific user including system and organization roles.", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/users/{{userId}}/roles", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "{{userId}}", "roles"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get user roles successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.roles).to.exist;", "});"]}}]}, {"name": "Transfer Owner Role", "description": "Transfer organization owner role from one user to another. Only current owners or system admins can perform this action.", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"organisation_id\": {{organisationId}}\n}"}, "url": {"raw": "{{baseURL}}/api/v1/users/{{userId}}/transfer-owner", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "{{userId}}", "transfer-owner"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Transfer owner role successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "});"]}}]}]}, {"name": "Invitation Management", "description": "Invitation management endpoints for creating and managing organization invitations. System admins can manage all invitations, while organization owners can only manage invitations for their own organizations.", "item": [{"name": "Get Invitations List", "description": "Get paginated list of invitations. System admins see all invitations, organization owners see only their own created invitations.", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/invitations", "host": ["{{baseURL}}"], "path": ["api", "v1", "invitations"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get invitations list successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.data).to.be.an('array');", "    pm.expect(jsonData.data.meta).to.exist;", "});"]}}]}, {"name": "Get Invitations with Filters", "description": "Get invitations with filtering by organization, role, and status.", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/invitations?model_type=App\\Models\\Organisation&model_id={{organisationId}}&role=member&per_page=10", "host": ["{{baseURL}}"], "path": ["api", "v1", "invitations"], "query": [{"key": "model_type", "value": "App\\Models\\Organisation"}, {"key": "model_id", "value": "{{organisationId}}"}, {"key": "role", "value": "member"}, {"key": "per_page", "value": "10"}]}}}, {"name": "Create Invitation", "description": "Create a new invitation for an organization. System admins can create invitations for any organization, organization owners can only create invitations for their own organizations.", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model_type\": \"App\\\\Models\\\\Organisation\",\n  \"model_id\": {{organisationId}},\n  \"role\": \"member\",\n  \"expires_at\": \"2025-06-25T12:00:00Z\",\n  \"max_uses\": 10,\n  \"email_restriction\": null\n}"}, "url": {"raw": "{{baseURL}}/api/v1/invitations", "host": ["{{baseURL}}"], "path": ["api", "v1", "invitations"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Create invitation successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.id).to.exist;", "    pm.expect(jsonData.data.model).to.exist;", "    pm.expect(jsonData.data.created_by).to.exist;", "    ", "    // Store invitation ID for other requests", "    pm.environment.set(\"invitationId\", jsonData.data.id);", "});"]}}]}, {"name": "Create Invitation with Email Restriction", "description": "Create an invitation with email restriction - only specific email can use this invitation.", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model_type\": \"App\\\\Models\\\\Organisation\",\n  \"model_id\": {{organisationId}},\n  \"role\": \"member\",\n  \"expires_at\": \"2025-06-25T12:00:00Z\",\n  \"max_uses\": 1,\n  \"email_restriction\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseURL}}/api/v1/invitations", "host": ["{{baseURL}}"], "path": ["api", "v1", "invitations"]}}}, {"name": "View Invitation Details (Public)", "description": "View invitation details without authentication. This is used to display invitation information to users before they accept.", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/invitations/{{invitationId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "invitations", "{{invitationId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 or 410\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 410]);", "});", "", "if (pm.response.code === 200) {", "    pm.test(\"View invitation successful\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.success).to.eql(true);", "        pm.expect(jsonData.data.id).to.exist;", "        pm.expect(jsonData.data.model).to.exist;", "    });", "} else if (pm.response.code === 410) {", "    pm.test(\"Invitation expired or reached limit\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.success).to.eql(false);", "    });", "}"]}}]}, {"name": "Accept Invitation", "description": "Accept an invitation and join the organization with the specified role. Requires authentication.", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseURL}}/api/v1/invitations/{{invitationId}}/accept", "host": ["{{baseURL}}"], "path": ["api", "v1", "invitations", "{{invitationId}}", "accept"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200, 403, or 410\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 403, 410, 422]);", "});", "", "if (pm.response.code === 200) {", "    pm.test(\"Accept invitation successful\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.success).to.eql(true);", "        pm.expect(jsonData.data.user).to.exist;", "        pm.expect(jsonData.data.organisation).to.exist;", "        pm.expect(jsonData.data.role).to.exist;", "    });", "} else {", "    pm.test(\"Accept invitation failed with expected error\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.success).to.eql(false);", "        pm.expect(jsonData.message).to.exist;", "    });", "}"]}}]}]}, {"name": "Sync Management", "description": "Data synchronization management endpoints for system administrators. Allows viewing sync logs, triggering manual syncs, and retrying failed synchronizations.", "item": [{"name": "Get Sync Logs List", "description": "Get paginated list of synchronization logs. Only accessible by system administrators (Root/Admin).", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/sync/logs", "host": ["{{baseURL}}"], "path": ["api", "v1", "sync", "logs"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get sync logs list successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.data).to.be.an('array');", "    pm.expect(jsonData.data.meta).to.exist;", "    ", "    // Store first sync log ID for other requests", "    if (jsonData.data.data.length > 0) {", "        pm.environment.set(\"syncLogId\", jsonData.data.data[0].id);", "    }", "});"]}}]}, {"name": "Get Sync Logs by Status", "description": "Filter sync logs by status (pending, running, completed, failed).", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/sync/logs?status=completed", "host": ["{{baseURL}}"], "path": ["api", "v1", "sync", "logs"], "query": [{"key": "status", "value": "completed"}]}}}, {"name": "Get Sync Logs by Type", "description": "Filter sync logs by sync type (product_sync, inventory_sync, etc.).", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/sync/logs?sync_type=product_sync", "host": ["{{baseURL}}"], "path": ["api", "v1", "sync", "logs"], "query": [{"key": "sync_type", "value": "product_sync"}]}}}, {"name": "Get Sync Log Details", "description": "Get detailed information about a specific sync log including all sync records.", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/sync/logs/{{syncLogId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "sync", "logs", "{{syncLogId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get sync log details successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.id).to.exist;", "    pm.expect(jsonData.data.batch_id).to.exist;", "    pm.expect(jsonData.data.status).to.exist;", "    pm.expect(jsonData.data.records).to.be.an('array');", "});"]}}]}, {"name": "Trigger Manual Sync - Full", "description": "Manually trigger a full synchronization with default settings.", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseURL}}/api/v1/sync/trigger", "host": ["{{baseURL}}"], "path": ["api", "v1", "sync", "trigger"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Trigger sync successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.batch_id).to.exist;", "    pm.expect(jsonData.data.status).to.exist;", "    ", "    // Store batch ID for retry testing", "    pm.environment.set(\"batchId\", jsonData.data.batch_id);", "});"]}}]}, {"name": "Trigger Manual Sync - Incremental", "description": "Manually trigger an incremental synchronization with custom settings.", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"incremental\": true,\n  \"batch_size\": 500,\n  \"timeout\": 3600\n}"}, "url": {"raw": "{{baseURL}}/api/v1/sync/trigger", "host": ["{{baseURL}}"], "path": ["api", "v1", "sync", "trigger"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Trigger incremental sync successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.batch_id).to.exist;", "    pm.expect(jsonData.data.status).to.exist;", "});"]}}]}, {"name": "Retry Failed Sync", "description": "Retry a failed synchronization operation. Only works for sync logs with 'failed' status.", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseURL}}/api/v1/sync/retry/{{syncLogId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "sync", "retry", "{{syncLogId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 or 422\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 422]);", "});", "", "if (pm.response.code === 200) {", "    pm.test(\"Retry sync successful\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.success).to.eql(true);", "        pm.expect(jsonData.data.new_batch_id).to.exist;", "        pm.expect(jsonData.data.original_batch_id).to.exist;", "    });", "} else if (pm.response.code === 422) {", "    pm.test(\"Retry validation error (sync not failed)\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.success).to.eql(false);", "        pm.expect(jsonData.errors.current_status).to.exist;", "    });", "}"]}}]}, {"name": "Get Sync Progress by <PERSON><PERSON> ID", "description": "Get real-time progress information for a sync operation using batch ID.", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/sync/progress?batch_id={{batchId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "sync", "progress"], "query": [{"key": "batch_id", "value": "{{batchId}}"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 or 404\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404]);", "});", "", "if (pm.response.code === 200) {", "    pm.test(\"Get sync progress successful\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.success).to.eql(true);", "        pm.expect(jsonData.data.batch_id).to.exist;", "        pm.expect(jsonData.data.status).to.exist;", "        pm.expect(jsonData.data.status).to.be.oneOf(['pending', 'processing', 'validating', 'completed', 'failed']);", "        pm.expect(jsonData.data.percentage).to.be.a('number');", "        ", "        // Check validation-specific fields when status is validating or completed", "        if (jsonData.data.status === 'validating') {", "            pm.expect(jsonData.data.sync_completed_at).to.exist;", "            pm.expect(jsonData.data.validation_job_id).to.exist;", "            pm.expect(jsonData.data.validation_started_at).to.exist;", "        }", "        ", "        if (jsonData.data.status === 'completed') {", "            pm.expect(jsonData.data.validation_results).to.exist;", "            pm.expect(jsonData.data.completed_at).to.exist;", "        }", "    });", "} else if (pm.response.code === 404) {", "    pm.test(\"Progress not found\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.success).to.eql(false);", "    });", "}"]}}]}, {"name": "Get Sync Progress by Job ID", "description": "Get real-time progress information for a sync operation using job ID.", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/sync/progress?job_id={{jobId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "sync", "progress"], "query": [{"key": "job_id", "value": "{{jobId}}"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 or 404\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404]);", "});", "", "if (pm.response.code === 200) {", "    pm.test(\"Get sync progress successful\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.success).to.eql(true);", "        pm.expect(jsonData.data.job_id).to.exist;", "        pm.expect(jsonData.data.status).to.exist;", "        pm.expect(jsonData.data.status).to.be.oneOf(['pending', 'processing', 'validating', 'completed', 'failed']);", "        pm.expect(jsonData.data.percentage).to.be.a('number');", "        ", "        // Check validation-specific fields when status is validating or completed", "        if (jsonData.data.status === 'validating') {", "            pm.expect(jsonData.data.sync_completed_at).to.exist;", "            pm.expect(jsonData.data.validation_job_id).to.exist;", "            pm.expect(jsonData.data.validation_started_at).to.exist;", "        }", "        ", "        if (jsonData.data.status === 'completed') {", "            pm.expect(jsonData.data.validation_results).to.exist;", "            pm.expect(jsonData.data.completed_at).to.exist;", "        }", "    });", "} else if (pm.response.code === 404) {", "    pm.test(\"Progress not found\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.success).to.eql(false);", "    });", "}"]}}]}, {"name": "Get Active Sync Jobs", "description": "Get list of all currently active sync jobs with their status and progress.", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/sync/active-jobs", "host": ["{{baseURL}}"], "path": ["api", "v1", "sync", "active-jobs"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get active sync jobs successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.active_jobs).to.be.an('array');", "    pm.expect(jsonData.data.count).to.be.a('number');", "});"]}}]}]}, {"name": "Report Management", "description": "Report management endpoints for viewing and exporting sales, volume, refunds, and order status reports. Implements role-based access control based on the existing four-tier permission system.", "item": [{"name": "Get Sales Report", "description": "Get sales amount statistics grouped by time period and region. Requires sales report viewing permissions.", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/reports/sales?start_date=2025-06-01&end_date=2025-06-30&group_by=month&organisation_id={{organisationId}}&currency=USD", "host": ["{{baseURL}}"], "path": ["api", "v1", "reports", "sales"], "query": [{"key": "start_date", "value": "2025-06-01", "description": "Start date (YYYY-MM-DD)"}, {"key": "end_date", "value": "2025-06-30", "description": "End date (YYYY-MM-DD)"}, {"key": "group_by", "value": "month", "description": "Group by: day, week, month, quarter, year"}, {"key": "currency", "value": "USD", "description": "Currency code (ISO 3-letter)"}, {"key": "countries", "value": "", "description": "Country codes array (ISO 2-letter)", "disabled": true}, {"key": "states", "value": "", "description": "Order states array", "disabled": true}, {"key": "payment_states", "value": "", "description": "Payment states array", "disabled": true}, {"key": "organisation_id", "value": "{{organisationId}}", "description": "Organization ID (required)"}, {"key": "product_id", "value": "", "description": "Product ID for filtering specific product sales data", "disabled": true}, {"key": "timezone", "value": "", "description": "Timezone", "disabled": true}, {"key": "include_refunds", "value": "", "description": "Include refunds (boolean)", "disabled": true}, {"key": "refund_status", "value": "", "description": "Refund status", "disabled": true}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has success field\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success', true);", "});", "", "pm.test(\"Response has sales data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('summary');", "    pm.expect(jsonData.data).to.have.property('data');", "});"]}}]}, {"name": "Get Volume Report", "description": "Get sales volume statistics grouped by time period and region. Requires volume report viewing permissions.", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/reports/volume?start_date=2025-06-01&end_date=2025-06-30&group_by=month&organisation_id={{organisationId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "reports", "volume"], "query": [{"key": "start_date", "value": "2025-06-01", "description": "Start date (YYYY-MM-DD)"}, {"key": "end_date", "value": "2025-06-30", "description": "End date (YYYY-MM-DD)"}, {"key": "group_by", "value": "month", "description": "Group by: day, week, month, quarter, year"}, {"key": "countries", "value": "", "description": "Country codes array (ISO 2-letter)", "disabled": true}, {"key": "states", "value": "", "description": "Order states array", "disabled": true}, {"key": "payment_states", "value": "", "description": "Payment states array", "disabled": true}, {"key": "organisation_id", "value": "{{organisationId}}", "description": "Organization ID (required)"}, {"key": "product_id", "value": "", "description": "Product ID for filtering specific product volume data", "disabled": true}, {"key": "timezone", "value": "", "description": "Timezone", "disabled": true}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has volume data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('summary');", "    pm.expect(jsonData.data.summary).to.have.property('total_quantity');", "});"]}}]}, {"name": "Get Refunds Report", "description": "Get refund statistics and analysis data. Requires refunds report viewing permissions.", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/reports/refunds?start_date=2025-06-01&end_date=2025-06-30&group_by=month&organisation_id={{organisationId}}&currency=USD", "host": ["{{baseURL}}"], "path": ["api", "v1", "reports", "refunds"], "query": [{"key": "start_date", "value": "2025-06-01", "description": "Start date (YYYY-MM-DD)"}, {"key": "end_date", "value": "2025-06-30", "description": "End date (YYYY-MM-DD)"}, {"key": "group_by", "value": "month", "description": "Group by: day, week, month, quarter, year"}, {"key": "currency", "value": "USD", "description": "Currency code (ISO 3-letter)"}, {"key": "countries", "value": "", "description": "Country codes array (ISO 2-letter)", "disabled": true}, {"key": "states", "value": "", "description": "Order states array", "disabled": true}, {"key": "payment_states", "value": "", "description": "Payment states array", "disabled": true}, {"key": "organisation_id", "value": "{{organisationId}}", "description": "Organization ID (required)"}, {"key": "product_id", "value": "", "description": "Product ID for filtering specific product refund data", "disabled": true}, {"key": "timezone", "value": "", "description": "Timezone", "disabled": true}, {"key": "refund_status", "value": "", "description": "Refund status filter", "disabled": true}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has refunds data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('summary');", "    pm.expect(jsonData.data.summary).to.have.property('total_refunds');", "    pm.expect(jsonData.data.summary).to.have.property('refund_rate');", "});"]}}]}, {"name": "Get Order Status Report", "description": "Get order status distribution and statistics. Requires order status report viewing permissions.", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/reports/order-status?start_date=2025-06-01&end_date=2025-06-30&group_by=month&organisation_id={{organisationId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "reports", "order-status"], "query": [{"key": "start_date", "value": "2025-06-01", "description": "Start date (YYYY-MM-DD)"}, {"key": "end_date", "value": "2025-06-30", "description": "End date (YYYY-MM-DD)"}, {"key": "group_by", "value": "month", "description": "Group by: day, week, month, quarter, year"}, {"key": "countries", "value": "", "description": "Country codes array (ISO 2-letter)", "disabled": true}, {"key": "states", "value": "", "description": "Order states array", "disabled": true}, {"key": "payment_states", "value": "", "description": "Payment states array", "disabled": true}, {"key": "organisation_id", "value": "{{organisationId}}", "description": "Organization ID (required)"}, {"key": "product_id", "value": "", "description": "Product ID for filtering specific product order status data", "disabled": true}, {"key": "timezone", "value": "", "description": "Timezone", "disabled": true}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has order status data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('summary');", "    pm.expect(jsonData.data.summary).to.have.property('total_orders');", "    pm.expect(jsonData.data.data[0]).to.have.property('status_distribution');", "});"]}}]}, {"name": "Get Product Ranking Report", "description": "Get product sales ranking data with dual rankings by sales amount and quantity. Includes comprehensive filtering options and chart-compatible data format. Requires product ranking report viewing permissions.", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/reports/product-ranking?start_date=2025-06-01&end_date=2025-06-30&organisation_id={{organisationId}}&limit=10&currency=USD", "host": ["{{baseURL}}"], "path": ["api", "v1", "reports", "product-ranking"], "query": [{"key": "start_date", "value": "2025-06-01", "description": "Start date (YYYY-MM-DD)"}, {"key": "end_date", "value": "2025-06-30", "description": "End date (YYYY-MM-DD)"}, {"key": "organisation_id", "value": "{{organisationId}}", "description": "Organization ID (required)"}, {"key": "limit", "value": "10", "description": "Number of products to return (1-100, default: 10)"}, {"key": "currency", "value": "USD", "description": "Currency code (ISO 3-letter code)"}, {"key": "timezone", "value": "", "description": "Timezone", "disabled": true}, {"key": "group_by", "value": "", "description": "Group by period (day, week, month, quarter, year)", "disabled": true}, {"key": "countries", "value": "", "description": "Country codes array (ISO 2-letter codes)", "disabled": true}, {"key": "states", "value": "", "description": "Order states array (completed, cancelled, processing, pending)", "disabled": true}, {"key": "payment_states", "value": "", "description": "Payment states array (completed, pending, failed, cancelled)", "disabled": true}, {"key": "include_refunds", "value": "", "description": "Include refund data (boolean)", "disabled": true}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has product ranking data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('sales_rankings');", "    pm.expect(jsonData.data).to.have.property('quantity_rankings');", "    pm.expect(jsonData.data).to.have.property('sales_ranking_chart');", "    pm.expect(jsonData.data).to.have.property('quantity_ranking_chart');", "    pm.expect(jsonData.data).to.have.property('summary');", "    pm.expect(jsonData.data.summary).to.have.property('limit');", "    pm.expect(jsonData.data.summary).to.have.property('total_products');", "    pm.expect(jsonData.data.summary).to.have.property('period');", "});", "", "pm.test(\"Sales and quantity rankings have correct structure\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.data.sales_rankings.length > 0) {", "        var salesRanking = jsonData.data.sales_rankings[0];", "        pm.expect(salesRanking).to.have.property('rank');", "        pm.expect(salesRanking).to.have.property('store_variant_id');", "        pm.expect(salesRanking).to.have.property('product_name');", "        pm.expect(salesRanking).to.have.property('product_slug');", "        pm.expect(salesRanking).to.have.property('product_package');", "        pm.expect(salesRanking).to.have.property('total_quantity');", "        pm.expect(salesRanking).to.have.property('total_sales');", "        pm.expect(salesRanking).to.have.property('total_sales_formatted');", "    }", "    if (jsonData.data.quantity_rankings.length > 0) {", "        var quantityRanking = jsonData.data.quantity_rankings[0];", "        pm.expect(quantityRanking).to.have.property('rank');", "        pm.expect(quantityRanking).to.have.property('store_variant_id');", "        pm.expect(quantityRanking).to.have.property('product_name');", "    }", "});"]}}]}, {"name": "Export Report - Sales (XLSX)", "description": "Export sales report data to Excel format. Requires report export permissions.", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"report_type\": \"sales\",\n  \"format\": \"xlsx\",\n  \"filename\": \"sales_report_june_2025\",\n  \"include_summary\": true,\n  \"include_charts\": false,\n  \"max_records\": 5000,\n  \"start_date\": \"2025-06-01\",\n  \"end_date\": \"2025-06-30\",\n  \"group_by\": \"day\",\n  \"organisation_id\": 1,\n  \"currency\": \"USD\",\n  \"product_id\": 1001\n}"}, "url": {"raw": "{{baseURL}}/api/v1/reports/export", "host": ["{{baseURL}}"], "path": ["api", "v1", "reports", "export"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has export data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('export_id');", "    pm.expect(jsonData.data).to.have.property('filename');", "    pm.expect(jsonData.data).to.have.property('format', 'xlsx');", "});"]}}]}, {"name": "Export Report - Volume (CSV)", "description": "Export volume report data to CSV format with email notification.", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"report_type\": \"volume\",\n  \"format\": \"csv\",\n  \"filename\": \"volume_report_june_2025\",\n  \"include_summary\": true,\n  \"max_records\": 10000,\n  \"email_to\": \"<EMAIL>\",\n  \"start_date\": \"2025-06-01\",\n  \"end_date\": \"2025-06-30\",\n  \"group_by\": \"week\",\n  \"organisation_id\": 1,\n  \"product_id\": 1002\n}"}, "url": {"raw": "{{baseURL}}/api/v1/reports/export", "host": ["{{baseURL}}"], "path": ["api", "v1", "reports", "export"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has export data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('export_id');", "    pm.expect(jsonData.data).to.have.property('format', 'csv');", "});"]}}]}, {"name": "Export Report - Async Large Dataset", "description": "Export large report dataset asynchronously with progress tracking.", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"report_type\": \"sales\",\n  \"format\": \"xlsx\",\n  \"filename\": \"large_sales_report\",\n  \"include_summary\": true,\n  \"include_charts\": true,\n  \"max_records\": 50000,\n  \"async\": true,\n  \"email_to\": \"<EMAIL>\",\n  \"start_date\": \"2025-01-01\",\n  \"end_date\": \"2025-06-30\",\n  \"group_by\": \"day\",\n  \"organisation_id\": 1,\n  \"currency\": \"USD\",\n  \"product_id\": 1003\n}"}, "url": {"raw": "{{baseURL}}/api/v1/reports/export", "host": ["{{baseURL}}"], "path": ["api", "v1", "reports", "export"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response indicates async processing\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('async', true);", "    pm.expect(jsonData.data).to.have.property('status', 'processing');", "    pm.expect(jsonData.data).to.have.property('progress_url');", "});"]}}]}, {"name": "Export Report - Product Ranking (XLSX)", "description": "Export product ranking report data to Excel format. Requires report export permissions.", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"report_type\": \"product_ranking\",\n  \"format\": \"xlsx\",\n  \"filename\": \"product_ranking_report_june_2025\",\n  \"include_summary\": true,\n  \"include_charts\": true,\n  \"max_records\": 1000,\n  \"start_date\": \"2025-06-01\",\n  \"end_date\": \"2025-06-30\",\n  \"organisation_id\": 1,\n  \"limit\": 50,\n  \"currency\": \"USD\",\n  \"product_id\": 1004\n}"}, "url": {"raw": "{{baseURL}}/api/v1/reports/export", "host": ["{{baseURL}}"], "path": ["api", "v1", "reports", "export"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has export data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('export_id');", "    pm.expect(jsonData.data).to.have.property('filename');", "    pm.expect(jsonData.data).to.have.property('download_url');", "    pm.expect(jsonData.data.report_type).to.equal('product_ranking');", "});"]}}]}]}], "variable": [{"key": "baseURL", "value": "http://localhost"}, {"key": "authToken", "value": ""}, {"key": "testEmail", "value": "<EMAIL>"}, {"key": "testPassword", "value": "password"}, {"key": "organisationId", "value": ""}, {"key": "roleId", "value": ""}, {"key": "invitationId", "value": ""}, {"key": "syncLogId", "value": ""}, {"key": "batchId", "value": ""}]}