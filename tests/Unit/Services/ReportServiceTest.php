<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Models\Organisation;
use App\Models\Order;
use App\Models\Role;
use App\Models\User;
use App\Services\ReportService;
use App\Services\SalesReportService;
use App\Services\RefundReportService;
use App\Services\OrderStatusReportService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

final class ReportServiceTest extends TestCase
{
    use RefreshDatabase;

    public function test_report_service_with_system_admin_user(): void
    {
        // Create organisation
        $organisation = Organisation::factory()->create([
            'code' => 'TEST001',
            'name' => 'Test Organisation',
        ]);

        // Create system root role
        $systemRootRole = Role::firstOrCreate([
            'name' => 'root',
            'guard_name' => 'system',
        ], [
            'organisation_id' => null,
        ]);

        // Create system root user
        $systemRootUser = User::factory()->create(['name' => 'System Root']);

        // Assign system role
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $systemRootUser->roles()->attach($systemRootRole);

        // Authenticate user
        Sanctum::actingAs($systemRootUser);

        // Test hasSystemAdminAccess
        $this->assertTrue($systemRootUser->hasSystemAdminAccess());

        // Create ReportService
        $salesReportService = new SalesReportService();
        $refundReportService = new RefundReportService();
        $orderStatusReportService = new OrderStatusReportService();
        $reportService = new ReportService($salesReportService, $refundReportService, $orderStatusReportService);

        // Test getSalesReport with minimal filters
        $filters = [
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'product_ids' => [1001, 1002],
        ];

        // This should not throw an exception
        $result = $reportService->getSalesReport($filters);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('summary', $result);
        $this->assertArrayHasKey('chart_data', $result);
        $this->assertArrayHasKey('region_data', $result);
        $this->assertArrayHasKey('meta', $result);
    }

    public function test_report_service_with_organisation_user(): void
    {
        // Create organisation
        $organisation = Organisation::factory()->create([
            'code' => 'TEST001',
            'name' => 'Test Organisation',
        ]);

        // Create organisation owner role
        $ownerRole = Role::firstOrCreate([
            'name' => 'owner',
            'guard_name' => 'api',
            'organisation_id' => $organisation->id,
        ]);

        // Create owner user
        $ownerUser = User::factory()->create(['name' => 'Owner User']);

        // Associate user with organisation
        $ownerUser->organisations()->attach($organisation->id);

        // Assign organisation role
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisation->id);
        $ownerUser->assignRole($ownerRole);

        // Authenticate user
        Sanctum::actingAs($ownerUser);

        // Test user methods
        $this->assertFalse($ownerUser->hasSystemAdminAccess());
        $this->assertEquals([$organisation->id], $ownerUser->getOrganisationIds()->toArray());

        // Create ReportService
        $salesReportService = new SalesReportService();
        $refundReportService = new RefundReportService();
        $orderStatusReportService = new OrderStatusReportService();
        $reportService = new ReportService($salesReportService, $refundReportService, $orderStatusReportService);

        // Test getSalesReport with minimal filters
        $filters = [
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'product_ids' => [1001, 1002],
        ];

        // This should not throw an exception
        $result = $reportService->getSalesReport($filters);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('summary', $result);
        $this->assertArrayHasKey('chart_data', $result);
        $this->assertArrayHasKey('region_data', $result);
        $this->assertArrayHasKey('meta', $result);
    }

    public function test_get_volume_report_with_system_admin(): void
    {
        // Create organisation
        $organisation = Organisation::factory()->create([
            'code' => 'TEST001',
            'name' => 'Test Organisation',
        ]);

        // Create system root role
        $systemRootRole = Role::firstOrCreate([
            'name' => 'root',
            'guard_name' => 'system',
        ], [
            'organisation_id' => null,
        ]);

        // Create system root user
        $systemRootUser = User::factory()->create(['name' => 'System Root']);

        // Assign system role
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $systemRootUser->roles()->attach($systemRootRole);

        // Authenticate user
        Sanctum::actingAs($systemRootUser);

        // Create ReportService
        $salesReportService = new SalesReportService();
        $refundReportService = new RefundReportService();
        $orderStatusReportService = new OrderStatusReportService();
        $reportService = new ReportService($salesReportService, $refundReportService, $orderStatusReportService);

        // Test getVolumeReport
        $filters = [
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'product_ids' => [1001, 1002],
        ];

        $result = $reportService->getVolumeReport($filters);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('summary', $result);
        $this->assertArrayHasKey('chart_data', $result);
        $this->assertArrayHasKey('region_data', $result);
        $this->assertArrayHasKey('meta', $result);
    }

    public function test_get_refund_report_with_organisation_user(): void
    {
        // Create organisation
        $organisation = Organisation::factory()->create([
            'code' => 'TEST001',
            'name' => 'Test Organisation',
        ]);

        // Create organisation owner role
        $ownerRole = Role::firstOrCreate([
            'name' => 'owner',
            'guard_name' => 'api',
            'organisation_id' => $organisation->id,
        ]);

        // Create owner user
        $ownerUser = User::factory()->create(['name' => 'Owner User']);

        // Associate user with organisation
        $ownerUser->organisations()->attach($organisation->id);

        // Assign organisation role
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisation->id);
        $ownerUser->assignRole($ownerRole);

        // Authenticate user
        Sanctum::actingAs($ownerUser);

        // Create ReportService
        $salesReportService = new SalesReportService();
        $refundReportService = new RefundReportService();
        $orderStatusReportService = new OrderStatusReportService();
        $reportService = new ReportService($salesReportService, $refundReportService, $orderStatusReportService);

        // Test getRefundReport
        $filters = [
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'product_ids' => [1001, 1002],
            'refund_status' => 'success',
        ];

        $result = $reportService->getRefundReport($filters);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('summary', $result);
        $this->assertArrayHasKey('chart_data', $result);
        $this->assertArrayHasKey('region_data', $result);
        $this->assertArrayHasKey('meta', $result);
    }

    public function test_get_order_status_report(): void
    {
        // Create organisation
        $organisation = Organisation::factory()->create([
            'code' => 'TEST001',
            'name' => 'Test Organisation',
        ]);

        // Create system root role
        $systemRootRole = Role::firstOrCreate([
            'name' => 'root',
            'guard_name' => 'system',
        ], [
            'organisation_id' => null,
        ]);

        // Create system root user
        $systemRootUser = User::factory()->create(['name' => 'System Root']);

        // Assign system role
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $systemRootUser->roles()->attach($systemRootRole);

        // Authenticate user
        Sanctum::actingAs($systemRootUser);

        // Create ReportService
        $salesReportService = new SalesReportService();
        $refundReportService = new RefundReportService();
        $orderStatusReportService = new OrderStatusReportService();
        $reportService = new ReportService($salesReportService, $refundReportService, $orderStatusReportService);

        // Test getOrderStatusReport
        $filters = [
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'product_ids' => [1001, 1002],
            'payment_states' => ['completed'],
        ];

        $result = $reportService->getOrderStatusReport($filters);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('summary', $result);
        $this->assertArrayHasKey('chart_data', $result);
        $this->assertArrayHasKey('region_data', $result);
        $this->assertArrayHasKey('meta', $result);
    }

    public function test_export_report_returns_placeholder_message(): void
    {
        // Create system root role
        $systemRootRole = Role::firstOrCreate([
            'name' => 'root',
            'guard_name' => 'system',
        ], [
            'organisation_id' => null,
        ]);

        // Create system root user
        $systemRootUser = User::factory()->create(['name' => 'System Root']);

        // Assign system role
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $systemRootUser->roles()->attach($systemRootRole);

        // Authenticate user
        Sanctum::actingAs($systemRootUser);

        // Create ReportService
        $salesReportService = new SalesReportService();
        $refundReportService = new RefundReportService();
        $orderStatusReportService = new OrderStatusReportService();
        $reportService = new ReportService($salesReportService, $refundReportService, $orderStatusReportService);

        // Test exportReport
        $config = [
            'format' => 'csv',
            'report_type' => 'sales',
            'filters' => [
                'start_date' => '2024-01-01',
                'end_date' => '2024-01-31',
            ],
        ];

        $result = $reportService->exportReport($config);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('message', $result);
        $this->assertArrayHasKey('config', $result);
        $this->assertEquals('Export functionality will be implemented in phase 5', $result['message']);
        $this->assertEquals($config, $result['config']);
    }

    public function test_reports_use_caching(): void
    {
        // Create system root role
        $systemRootRole = Role::firstOrCreate([
            'name' => 'root',
            'guard_name' => 'system',
        ], [
            'organisation_id' => null,
        ]);

        // Create system root user
        $systemRootUser = User::factory()->create(['name' => 'System Root']);

        // Assign system role
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $systemRootUser->roles()->attach($systemRootRole);

        // Authenticate user
        Sanctum::actingAs($systemRootUser);

        // Clear cache
        Cache::flush();

        // Create ReportService
        $salesReportService = new SalesReportService();
        $refundReportService = new RefundReportService();
        $orderStatusReportService = new OrderStatusReportService();
        $reportService = new ReportService($salesReportService, $refundReportService, $orderStatusReportService);

        $filters = [
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'product_ids' => [1001, 1002],
        ];

        // First call should cache the result
        $result1 = $reportService->getSalesReport($filters);

        // Second call should return cached result
        $result2 = $reportService->getSalesReport($filters);

        $this->assertEquals($result1, $result2);
    }



    public function test_reports_with_various_filters(): void
    {
        // Create organisation
        $organisation = Organisation::factory()->create([
            'code' => 'TEST001',
            'name' => 'Test Organisation',
        ]);

        // Create system root role
        $systemRootRole = Role::firstOrCreate([
            'name' => 'root',
            'guard_name' => 'system',
        ], [
            'organisation_id' => null,
        ]);

        // Create system root user
        $systemRootUser = User::factory()->create(['name' => 'System Root']);

        // Assign system role
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $systemRootUser->roles()->attach($systemRootRole);

        // Authenticate user
        Sanctum::actingAs($systemRootUser);

        // Create ReportService
        $salesReportService = new SalesReportService();
        $refundReportService = new RefundReportService();
        $orderStatusReportService = new OrderStatusReportService();
        $reportService = new ReportService($salesReportService, $refundReportService, $orderStatusReportService);

        // Test with comprehensive filters
        $filters = [
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'product_ids' => [1001, 1002],
            'countries' => ['US', 'CA'],
            'states' => ['CA', 'NY'],
            'payment_states' => ['completed', 'pending'],
            'include_refunds' => false,
            'refund_status' => 'success',
        ];

        $result = $reportService->getSalesReport($filters);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('summary', $result);
        $this->assertArrayHasKey('chart_data', $result);
        $this->assertArrayHasKey('region_data', $result);
        $this->assertArrayHasKey('meta', $result);
    }

    public function test_reports_with_empty_product_ids(): void
    {
        // Create organisation
        $organisation = Organisation::factory()->create([
            'code' => 'TEST001',
            'name' => 'Test Organisation',
        ]);

        // Create system root role
        $systemRootRole = Role::firstOrCreate([
            'name' => 'root',
            'guard_name' => 'system',
        ], [
            'organisation_id' => null,
        ]);

        // Create system root user
        $systemRootUser = User::factory()->create(['name' => 'System Root']);

        // Assign system role
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $systemRootUser->roles()->attach($systemRootRole);

        // Authenticate user
        Sanctum::actingAs($systemRootUser);

        // Create ReportService
        $salesReportService = new SalesReportService();
        $refundReportService = new RefundReportService();
        $orderStatusReportService = new OrderStatusReportService();
        $reportService = new ReportService($salesReportService, $refundReportService, $orderStatusReportService);

        // Test with empty product_ids array (should return no results)
        $filters = [
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'product_ids' => [], // Empty array
        ];

        $result = $reportService->getSalesReport($filters);

        $this->assertIsArray($result);
        // Should handle empty results gracefully
    }
}
