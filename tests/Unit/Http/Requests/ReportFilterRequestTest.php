<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Requests;

use App\Http\Requests\Api\V1\ReportFilterRequest;
use App\Models\Organisation;
use App\Models\Product;
use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

/**
 * Test ReportFilterRequest validation logic, especially product access permissions
 */
final class ReportFilterRequestTest extends TestCase
{
    use RefreshDatabase;

    private User $systemAdminUser;
    private User $ownerUser;
    private User $memberUser;
    private User $unauthorizedUser;
    private Organisation $organisation1;
    private Organisation $organisation2;
    private Product $org1Product1;
    private Product $org1Product2;
    private Product $org2Product1;
    private Role $ownerRole;
    private Role $memberRole;

    protected function setUp(): void
    {
        parent::setUp();

        // Create system admin user
        $this->systemAdminUser = User::factory()->create(['name' => 'System Admin']);
        $systemAdminRole = Role::firstOrCreate([
            'name' => 'admin',
            'guard_name' => 'system',
        ], [
            'organisation_id' => null,
        ]);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->systemAdminUser->roles()->attach($systemAdminRole);

        // Create organisations
        $this->organisation1 = Organisation::factory()->create([
            'code' => 'ORG001',
            'name' => 'Organisation 1',
        ]);
        $this->organisation2 = Organisation::factory()->create([
            'code' => 'ORG002',
            'name' => 'Organisation 2',
        ]);

        // Create products
        $this->org1Product1 = Product::create([
            'store_variant_id' => 1001,
            'owner_id' => 'ORG001',
            'sku' => 'ORG1-001',
            'code' => 'org1-product-1',
            'name' => 'Org1 Product 1',
            'slug' => 'org1-product-1',
            'enabled' => true,
            'current_price' => 1000,
        ]);

        $this->org1Product2 = Product::create([
            'store_variant_id' => 1002,
            'owner_id' => 'ORG001',
            'sku' => 'ORG1-002',
            'code' => 'org1-product-2',
            'name' => 'Org1 Product 2',
            'slug' => 'org1-product-2',
            'enabled' => true,
            'current_price' => 2000,
        ]);

        $this->org2Product1 = Product::create([
            'store_variant_id' => 2001,
            'owner_id' => 'ORG002',
            'sku' => 'ORG2-001',
            'code' => 'org2-product-1',
            'name' => 'Org2 Product 1',
            'slug' => 'org2-product-1',
            'enabled' => true,
            'current_price' => 3000,
        ]);

        // Create roles
        $this->ownerRole = Role::firstOrCreate([
            'name' => 'owner',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation1->id,
        ]);

        $this->memberRole = Role::firstOrCreate([
            'name' => 'member',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation1->id,
        ]);

        // Create users
        $this->ownerUser = User::factory()->create(['name' => 'Owner User']);
        $this->ownerUser->organisations()->attach($this->organisation1->id);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation1->id);
        $this->ownerUser->assignRole($this->ownerRole);

        $this->memberUser = User::factory()->create(['name' => 'Member User']);
        $this->memberUser->organisations()->attach($this->organisation1->id);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation1->id);
        $this->memberUser->assignRole($this->memberRole);

        $this->unauthorizedUser = User::factory()->create(['name' => 'Unauthorized User']);
        // This user doesn't belong to any organisation
    }

    public function test_system_admin_can_access_any_product(): void
    {
        Sanctum::actingAs($this->systemAdminUser);

        $request = new ReportFilterRequest();
        $request->replace([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation1->id,
            'product_id' => $this->org1Product1->store_variant_id,
        ]);
        $request->setUserResolver(fn() => $this->systemAdminUser);

        $validator = Validator::make($request->all(), $request->rules());
        $request->withValidator($validator);

        $this->assertTrue($validator->passes());
    }

    public function test_owner_can_access_own_organisation_products(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $request = new ReportFilterRequest();
        $request->replace([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation1->id,
            'product_id' => $this->org1Product1->store_variant_id,
        ]);
        $request->setUserResolver(fn() => $this->ownerUser);

        $validator = Validator::make($request->all(), $request->rules());
        $request->withValidator($validator);

        $this->assertTrue($validator->passes());
    }

    public function test_member_can_access_own_organisation_products(): void
    {
        Sanctum::actingAs($this->memberUser);

        $request = new ReportFilterRequest();
        $request->replace([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation1->id,
            'product_id' => $this->org1Product2->store_variant_id,
        ]);
        $request->setUserResolver(fn() => $this->memberUser);

        $validator = Validator::make($request->all(), $request->rules());
        $request->withValidator($validator);

        $this->assertTrue($validator->passes());
    }

    public function test_user_cannot_access_other_organisation_products(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $request = new ReportFilterRequest();
        $request->replace([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation2->id,
            'product_id' => $this->org2Product1->store_variant_id,
        ]);
        $request->setUserResolver(fn() => $this->ownerUser);

        $validator = Validator::make($request->all(), $request->rules());
        $request->withValidator($validator);

        $this->assertFalse($validator->passes());
        $this->assertTrue($validator->errors()->has('organisation_id'));
    }

    public function test_user_cannot_access_product_from_different_organisation_than_specified(): void
    {
        Sanctum::actingAs($this->ownerUser);

        // Try to access org2's product while specifying org1 as organisation_id
        // This tests the security vulnerability where user could potentially access
        // products from other organisations if validation was not properly implemented
        $request = new ReportFilterRequest();
        $request->replace([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation1->id,
            'product_id' => $this->org2Product1->store_variant_id, // Product from org2
        ]);
        $request->setUserResolver(fn() => $this->ownerUser);

        $validator = Validator::make($request->all(), $request->rules());
        $request->withValidator($validator);

        $this->assertFalse($validator->passes());
        $this->assertTrue($validator->errors()->has('product_id'));
        $this->assertEquals(
            __('errors.report_product_access_denied'),
            $validator->errors()->first('product_id')
        );
    }

    public function test_unauthorized_user_cannot_access_any_products(): void
    {
        Sanctum::actingAs($this->unauthorizedUser);

        $request = new ReportFilterRequest();
        $request->replace([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation1->id,
            'product_id' => $this->org1Product1->store_variant_id,
        ]);
        $request->setUserResolver(fn() => $this->unauthorizedUser);

        $validator = Validator::make($request->all(), $request->rules());
        $request->withValidator($validator);

        $this->assertFalse($validator->passes());
        $this->assertTrue($validator->errors()->has('organisation_id'));
    }

    public function test_product_validation_requires_organisation_id(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $request = new ReportFilterRequest();
        $request->replace([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'product_id' => $this->org1Product1->store_variant_id,
            // Missing organisation_id
        ]);
        $request->setUserResolver(fn() => $this->ownerUser);

        $validator = Validator::make($request->all(), $request->rules());
        $request->withValidator($validator);

        $this->assertFalse($validator->passes());
        // After refactoring, when organisation_id is missing, the basic validation rule should fail
        // because organisation_id is required for non-system users
        $this->assertTrue($validator->errors()->has('organisation_id'));
    }

    public function test_validation_passes_without_product_id(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $request = new ReportFilterRequest();
        $request->replace([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation1->id,
            // No product_id - should be valid
        ]);
        $request->setUserResolver(fn() => $this->ownerUser);

        $validator = Validator::make($request->all(), $request->rules());
        $request->withValidator($validator);

        $this->assertTrue($validator->passes());
    }

    /**
     * Test the specific security vulnerability: user trying to access a product
     * that exists in the database but doesn't belong to their organisation
     */
    public function test_security_vulnerability_user_cannot_access_product_outside_their_organisation(): void
    {
        // Create a third organisation with a product
        $organisation3 = Organisation::factory()->create([
            'code' => 'ORG003',
            'name' => 'Organisation 3',
        ]);

        $org3Product = Product::create([
            'store_variant_id' => 3001,
            'owner_id' => 'ORG003',
            'sku' => 'ORG3-001',
            'code' => 'org3-product-1',
            'name' => 'Org3 Product 1',
            'slug' => 'org3-product-1',
            'enabled' => true,
            'current_price' => 4000,
        ]);

        Sanctum::actingAs($this->ownerUser);

        // User from org1 tries to access product from org3 by specifying org1 as organisation_id
        // This should fail because the product doesn't belong to org1
        $request = new ReportFilterRequest();
        $request->replace([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation1->id, // User's organisation
            'product_id' => $org3Product->store_variant_id, // Product from different organisation
        ]);
        $request->setUserResolver(fn() => $this->ownerUser);

        $validator = Validator::make($request->all(), $request->rules());
        $request->withValidator($validator);

        $this->assertFalse($validator->passes());
        $this->assertTrue($validator->errors()->has('product_id'));
        $this->assertEquals(
            __('errors.report_product_access_denied'),
            $validator->errors()->first('product_id')
        );
    }

    /**
     * Test that non-existent product IDs are handled correctly
     */
    public function test_non_existent_product_id_fails_basic_validation(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $request = new ReportFilterRequest();
        $request->replace([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation1->id,
            'product_id' => 99999, // Non-existent product ID
        ]);
        $request->setUserResolver(fn() => $this->ownerUser);

        $validator = Validator::make($request->all(), $request->rules());
        $request->withValidator($validator);

        $this->assertFalse($validator->passes());
        $this->assertTrue($validator->errors()->has('product_id'));
        // This should fail at the basic 'exists:products,store_variant_id' rule level
    }

    /**
     * Test that system admin can access products from any organisation
     */
    public function test_system_admin_can_access_products_from_any_organisation(): void
    {
        Sanctum::actingAs($this->systemAdminUser);

        // System admin accessing org2 product
        $request = new ReportFilterRequest();
        $request->replace([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation2->id,
            'product_id' => $this->org2Product1->store_variant_id,
        ]);
        $request->setUserResolver(fn() => $this->systemAdminUser);

        $validator = Validator::make($request->all(), $request->rules());
        $request->withValidator($validator);

        $this->assertTrue($validator->passes());
    }

    /**
     * Test that getUserAccessibleProductIds method works correctly
     */
    public function test_get_user_accessible_product_ids_method(): void
    {
        // Use reflection to access the protected method
        $reflection = new \ReflectionClass(ReportFilterRequest::class);
        $method = $reflection->getMethod('getUserAccessibleProductIds');
        $method->setAccessible(true);

        // Test for owner user
        $request = new ReportFilterRequest();
        $request->setUserResolver(fn() => $this->ownerUser);
        $accessibleIds = $method->invoke($request, $this->organisation1->id);
        $this->assertContains($this->org1Product1->store_variant_id, $accessibleIds);
        $this->assertContains($this->org1Product2->store_variant_id, $accessibleIds);
        $this->assertNotContains($this->org2Product1->store_variant_id, $accessibleIds);

        // Test for system admin
        $request = new ReportFilterRequest();
        $request->setUserResolver(fn() => $this->systemAdminUser);
        $accessibleIds = $method->invoke($request, $this->organisation1->id);
        $this->assertContains($this->org1Product1->store_variant_id, $accessibleIds);
        $this->assertContains($this->org1Product2->store_variant_id, $accessibleIds);

        // Test for unauthorized user
        $request = new ReportFilterRequest();
        $request->setUserResolver(fn() => $this->unauthorizedUser);
        $accessibleIds = $method->invoke($request, $this->organisation1->id);
        $this->assertEmpty($accessibleIds);
    }
}
